"use server";

// import { createClient } from "@/utils/supabase/server";
import { createAdminClient } from "@/utils/supabase/admin";
import { getProductImagePath, getProductBaseImagePath, getProductVariantImagePath } from "@/lib/utils/storage-paths";

// Upload size limits (75MB for up to 5 images)
const MAX_UPLOAD_SIZE_BYTES = 75 * 1024 * 1024; // 75MB
const MAX_IMAGES_PER_GROUP = 5;

// Helper function to handle multiple image uploads
// Each image is processed individually and compressed to <100KB
// Total input: up to 5 × 15MB = 75MB, Total output: ~5 × 100KB = 500KB
export async function handleMultipleImageUpload(
  userId: string,
  productId: string,
  imageFiles: (File | null)[],
  existingImageUrls: string[] = [],
  removedIndices: number[] = [],
  pathType: 'base' | 'variant' = 'base',
  variantId?: string
): Promise<{ urls: string[]; error?: string }> {
  // Validate input parameters
  if (!userId || typeof userId !== 'string') {
    console.error('Invalid userId provided to handleMultipleImageUpload:', userId);
    return { urls: [], error: `Invalid userId: expected string, got ${typeof userId}` };
  }

  if (!productId || typeof productId !== 'string') {
    console.error('Invalid productId provided to handleMultipleImageUpload:', productId);
    return { urls: [], error: `Invalid productId: expected string, got ${typeof productId}` };
  }

  // Validate variantId if pathType is 'variant'
  if (pathType === 'variant' && (!variantId || typeof variantId !== 'string')) {
    console.error('Invalid variantId provided for variant path type:', variantId);
    return { urls: [], error: `Invalid variantId: expected string when pathType is 'variant', got ${typeof variantId}` };
  }

  // Validate upload limits
  const validImageFiles = imageFiles.filter(file => file !== null) as File[];

  if (validImageFiles.length === 0) {
    return { urls: existingImageUrls };
  }

  if (validImageFiles.length > MAX_IMAGES_PER_GROUP) {
    return { urls: [], error: `Maximum of ${MAX_IMAGES_PER_GROUP} images allowed per ${pathType === 'variant' ? 'variant' : 'product'}.` };
  }

  const totalUploadSize = validImageFiles.reduce((total, file) => total + file.size, 0);
  if (totalUploadSize > MAX_UPLOAD_SIZE_BYTES) {
    const totalSizeMB = (totalUploadSize / (1024 * 1024)).toFixed(1);
    const maxSizeMB = (MAX_UPLOAD_SIZE_BYTES / (1024 * 1024)).toFixed(0);
    return { urls: [], error: `Total upload size (${totalSizeMB}MB) exceeds the ${maxSizeMB}MB limit for ${pathType === 'variant' ? 'variant' : 'product'} images.` };
  }

  const bucketName = "business";
  const urls: string[] = [...existingImageUrls];

  // Individual image paths are generated using scalable structure in getProductImagePath utility

  // Use admin client for storage operations to bypass RLS
  const adminSupabase = createAdminClient();

  // First, handle removals
  for (const index of removedIndices) {
    if (index >= 0 && index < existingImageUrls.length) {
      const imageUrl = existingImageUrls[index];
      if (imageUrl) {
        try {
          console.log(`Removing image at index ${index}: ${imageUrl}`);

          // Extract the storage path from the URL
          // Parse the URL to extract the correct path
          const url = new URL(imageUrl);
          const pathParts = url.pathname.split('/');

          // The path will be in format like /storage/v1/object/public/business/userId/products/productId_name/image_0.webp
          // We need to extract the part after 'business/'
          const businessIndex = pathParts.findIndex(part => part === 'business');

          if (businessIndex !== -1 && businessIndex < pathParts.length - 1) {
            // Extract the path after 'business/'
            const storagePath = pathParts.slice(businessIndex + 1).join('/').split('?')[0];

            console.log(`Attempting to delete from storage path: ${storagePath}`);

            // Delete the file from storage using admin client
            const { error: deleteError } = await adminSupabase.storage
              .from(bucketName)
              .remove([storagePath]);

            if (deleteError && deleteError.message !== "The resource was not found") {
              console.error(`Error deleting image at index ${index}:`, deleteError);
              console.error(`Delete error details:`, deleteError);
              // Don't fail the entire operation for storage deletion errors
            } else {
              console.log(`Successfully deleted image at path: ${storagePath}`);
            }
          } else {
            console.warn(`Could not extract storage path from URL: ${imageUrl}`);
            console.warn(`URL pathname: ${url.pathname}`);
            console.warn(`Path parts:`, pathParts);
          }
        } catch (error) {
          console.error(`Error processing image URL for deletion at index ${index}:`, error);
          // Don't fail the entire operation for individual image deletion errors
        }

        // Remove from the URLs array regardless of storage deletion success
        // This ensures the database is updated even if storage deletion fails
        urls[index] = '';
      }
    }
  }

  // Filter out empty strings from the URLs array to actually remove the deleted images
  let filteredUrls = urls.filter(url => url !== '');
  // Then, handle uploads

  for (let i = 0; i < imageFiles.length; i++) {
    const imageFile = imageFiles[i];
    if (!imageFile) {
      continue;
    }

    try {
      // Create path with scalable structure and precise timestamp to prevent caching
      const timestamp = new Date().getTime() + Math.floor(Math.random() * 1000);

      // Generate appropriate path based on type
      const imagePath = pathType === 'variant' && variantId
        ? getProductVariantImagePath(userId, productId, variantId, i, timestamp)
        : getProductBaseImagePath(userId, productId, i, timestamp);

      // File is already compressed on client-side, just upload it
      const fileBuffer = Buffer.from(await imageFile.arrayBuffer());

      const { error: uploadError } = await adminSupabase.storage
        .from(bucketName)
        .upload(imagePath, fileBuffer, {
          contentType: imageFile.type, // Use original file type (already compressed)
          upsert: true
        });

      if (uploadError) {
        console.error(`Failed to upload image ${i}:`, uploadError);
        continue;
      }

      const { data: urlData } = adminSupabase.storage
        .from(bucketName)
        .getPublicUrl(imagePath);

      if (!urlData?.publicUrl) {
        console.error(`Could not retrieve public URL for image ${i}`);
        continue;
      }



      // Update the URL in the array
      if (i < urls.length) {
        urls[i] = urlData.publicUrl;
      } else {
        urls.push(urlData.publicUrl);
      }
    } catch (error) {
      console.error(`Error processing image ${i}:`, error);
    }
  }

  // Update filteredUrls with the latest URLs that include new uploads
  filteredUrls = urls.filter(url => url !== '');



  // Return the filtered URLs (removing empty strings)
  return { urls: filteredUrls };
}

// Helper function specifically for base product images
export async function handleBaseProductImageUpload(
  userId: string,
  productId: string,
  imageFiles: (File | null)[],
  existingImageUrls: string[] = [],
  removedIndices: number[] = []
): Promise<{ urls: string[]; error?: string }> {
  return handleMultipleImageUpload(
    userId,
    productId,
    imageFiles,
    existingImageUrls,
    removedIndices,
    'base'
  );
}

// Helper function specifically for variant images
export async function handleVariantImageUpload(
  userId: string,
  productId: string,
  variantId: string,
  imageFiles: (File | null)[],
  existingImageUrls: string[] = [],
  removedIndices: number[] = []
): Promise<{ urls: string[]; error?: string }> {
  return handleMultipleImageUpload(
    userId,
    productId,
    imageFiles,
    existingImageUrls,
    removedIndices,
    'variant',
    variantId
  );
}

