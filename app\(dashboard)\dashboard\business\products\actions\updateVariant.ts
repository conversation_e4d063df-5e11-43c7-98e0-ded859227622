"use server";

import { createClient } from "@/utils/supabase/server";
import { revalidatePath } from "next/cache";
import { updateVariantFormSchema, VariantData } from "./schemas";
import { handleVariantImageUpload } from "./imageHandlers";

// Update an existing product variant
export async function updateProductVariant(
  variantId: string,
  formData: FormData
): Promise<{ success: boolean; error?: string; data?: VariantData }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user)
    return { success: false, error: "User not authenticated." };

  try {
    // First, verify that the variant belongs to the authenticated user
    const { data: existingVariant, error: variantError } = await supabase
      .from("product_variants")
      .select(`
        id,
        product_id,
        variant_name,
        variant_values,
        base_price,
        discounted_price,
        is_available,
        images,
        featured_image_index,
        products_services!inner(business_id)
      `)
      .eq("id", variantId)
      .single();

    if (variantError || !existingVariant) {
      return { success: false, error: "Variant not found." };
    }

    // Check if the product belongs to the authenticated user
    if ((existingVariant.products_services as any).business_id !== user.id) {
      return { success: false, error: "Access denied." };
    }

    // Extract form values
    const variant_name = formData.get("variant_name") as string;
    const variant_values_json = formData.get("variant_values") as string;
    const base_price = formData.get("base_price") as string;
    const discounted_price = formData.get("discounted_price") as string;
    const is_available = formData.get("is_available");
    const featured_image_index = formData.get("featured_image_index") as string;
    const remove_images = formData.get("remove_images") as string;

    // Parse variant values JSON if provided
    let variant_values: Record<string, string> | undefined;
    if (variant_values_json) {
      try {
        variant_values = JSON.parse(variant_values_json);
      } catch (error) {
        return { success: false, error: "Invalid variant values format." };
      }
    }

    // Prepare form values for validation (only include fields that are being updated)
    const formValues: any = {
      id: variantId,
      product_id: existingVariant.product_id,
    };

    if (variant_name !== null) formValues.variant_name = variant_name;
    if (variant_values) formValues.variant_values = variant_values;
    if (base_price !== null) formValues.base_price = base_price && base_price.trim() !== '' && base_price !== 'undefined' ? parseFloat(base_price) : null;
    if (discounted_price !== null) formValues.discounted_price = discounted_price && discounted_price.trim() !== '' && discounted_price !== 'undefined' ? parseFloat(discounted_price) : null;
    if (is_available !== null) formValues.is_available = is_available === "true";
    if (featured_image_index !== null) formValues.featured_image_index = parseInt(featured_image_index) || 0;

    // Validate form data
    const validatedFields = updateVariantFormSchema.safeParse(formValues);
    if (!validatedFields.success) {
      console.error(
        "Update Variant Validation Error:",
        validatedFields.error.flatten().fieldErrors
      );
      const errors = validatedFields.error.flatten().fieldErrors;
      const errorMessages = Object.entries(errors)
        .map(
          ([field, messages]) =>
            `${field}: ${
              Array.isArray(messages) ? messages.join(", ") : messages
            }`
        )
        .join("; ");
      return { success: false, error: `Invalid data: ${errorMessages}` };
    }

    // Check if variant combination already exists (if variant_values is being updated)
    if (variant_values) {
      const { data: existingVariants, error: checkError } = await supabase
        .from("product_variants")
        .select("id, variant_values")
        .eq("product_id", existingVariant.product_id)
        .neq("id", variantId);

      if (checkError) {
        console.error("Error checking existing variants:", checkError);
        return { success: false, error: "Failed to validate variant uniqueness." };
      }

      // Check for duplicate variant combinations manually
      if (existingVariants && existingVariants.length > 0) {
        const duplicateVariant = existingVariants.find(existing => {
          // Compare the variant_values objects
          const existingValues = typeof existing.variant_values === 'string'
            ? JSON.parse(existing.variant_values)
            : existing.variant_values;

          // Deep comparison of variant values
          const existingKeys = Object.keys(existingValues).sort();
          const newKeys = Object.keys(variant_values).sort();

          if (existingKeys.length !== newKeys.length) return false;

          return existingKeys.every(key =>
            newKeys.includes(key) && existingValues[key] === variant_values[key]
          );
        });

        if (duplicateVariant) {
          return { success: false, error: "A variant with this combination already exists." };
        }
      }
    }

    // Handle image operations
    let currentImages = [...(existingVariant.images || [])];
    
    // Remove images if specified
    if (remove_images) {
      try {
        const indicesToRemove: number[] = JSON.parse(remove_images);
        currentImages = currentImages.filter((_, index) => !indicesToRemove.includes(index));
      } catch (error) {
        console.error("Error parsing remove_images:", error);
      }
    }

    // Handle new image uploads
    const imageFiles: File[] = [];
    for (const [key, value] of formData.entries()) {
      if (key.startsWith("new_images[") && value instanceof File && value.size > 0) {
        imageFiles.push(value);
      }
    }

    // Upload new images if any
    if (imageFiles.length > 0) {
      const uploadResult = await handleVariantImageUpload(
        user.id,
        existingVariant.product_id,
        variantId,
        imageFiles,
        currentImages
      );

      if (uploadResult.error) {
        return { success: false, error: uploadResult.error || "Failed to upload images." };
      }

      currentImages = uploadResult.urls || [];
    }

    // Prepare update data
    const updateData: any = {};
    
    if (validatedFields.data.variant_name !== undefined) {
      updateData.variant_name = validatedFields.data.variant_name;
    }
    
    if (validatedFields.data.variant_values !== undefined) {
      updateData.variant_values = validatedFields.data.variant_values;
    }
    
    if (validatedFields.data.base_price !== undefined) {
      updateData.base_price = validatedFields.data.base_price;
    }
    
    if (validatedFields.data.discounted_price !== undefined) {
      updateData.discounted_price = validatedFields.data.discounted_price;
    }
    
    if (validatedFields.data.is_available !== undefined) {
      updateData.is_available = validatedFields.data.is_available;
    }

    // Update images and featured image index
    updateData.images = currentImages;
    updateData.featured_image_index = Math.min(
      validatedFields.data.featured_image_index || 0,
      Math.max(0, currentImages.length - 1)
    );

    // Update the variant
    const { data: updatedVariant, error: updateError } = await supabase
      .from("product_variants")
      .update(updateData)
      .eq("id", variantId)
      .select()
      .single();

    if (updateError) {
      console.error("Error updating variant:", updateError);
      return { success: false, error: "Failed to update variant." };
    }

    // Revalidate the products page
    revalidatePath("/dashboard/business/products");
    revalidatePath(`/dashboard/business/products/${existingVariant.product_id}`);

    return {
      success: true,
      data: {
        ...updatedVariant,
        variant_values: typeof updatedVariant.variant_values === 'string'
          ? JSON.parse(updatedVariant.variant_values)
          : updatedVariant.variant_values,
        created_at: new Date(updatedVariant.created_at),
        updated_at: new Date(updatedVariant.updated_at),
      },
    };
  } catch (error) {
    console.error("Unexpected error in updateProductVariant:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}

// Bulk update variants
export async function updateMultipleVariants(
  updates: Array<{
    id: string;
    data: {
      is_available?: boolean;
      base_price?: number;
      discounted_price?: number;
    };
  }>
): Promise<{ success: boolean; error?: string; updated_count?: number; failed_count?: number }> {
  const supabase = await createClient();
  const {
    data: { user },
    error: authError,
  } = await supabase.auth.getUser();
  if (authError || !user)
    return { success: false, error: "User not authenticated." };

  try {
    let updated_count = 0;
    let failed_count = 0;

    for (const update of updates) {
      try {
        // Verify ownership
        const { data: variant, error: variantError } = await supabase
          .from("product_variants")
          .select(`
            id,
            products_services!inner(business_id)
          `)
          .eq("id", update.id)
          .single();

        if (variantError || !variant || (variant.products_services as any).business_id !== user.id) {
          failed_count++;
          continue;
        }

        // Update the variant
        const { error: updateError } = await supabase
          .from("product_variants")
          .update(update.data)
          .eq("id", update.id);

        if (updateError) {
          failed_count++;
        } else {
          updated_count++;
        }
      } catch (error) {
        failed_count++;
      }
    }

    // Revalidate the products page
    revalidatePath("/dashboard/business/products");

    return {
      success: true,
      updated_count,
      failed_count,
    };
  } catch (error) {
    console.error("Unexpected error in updateMultipleVariants:", error);
    return { success: false, error: "An unexpected error occurred." };
  }
}
